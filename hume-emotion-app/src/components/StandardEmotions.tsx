'use client';

import { EmotionScore } from '@/types/analysis';

interface StandardEmotionsProps {
  emotions: EmotionScore[];
  analyzedEmotions: EmotionScore[];
  className?: string;
}

export default function StandardEmotions({ emotions, analyzedEmotions, className = '' }: StandardEmotionsProps) {
  const displayEmotions = emotions.slice(0, 10);

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <h3 className="text-xl font-semibold text-gray-800 mb-4">
        Detected Emotions
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {displayEmotions.map((emotion, index) => {
          const isAnalyzed = analyzedEmotions.some(ae => ae.name === emotion.name);
          
          return (
            <div
              key={emotion.name}
              className={`p-4 rounded-lg border transition-all duration-200 ${
                isAnalyzed
                  ? 'bg-gradient-to-br from-blue-50 to-purple-50 border-blue-200 shadow-sm'
                  : 'bg-gray-50 border-gray-200'
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <span className={`text-sm font-medium px-2 py-1 rounded ${
                    isAnalyzed ? 'bg-blue-100 text-blue-700' : 'bg-gray-200 text-gray-600'
                  }`}>
                    #{index + 1}
                  </span>
                  <span className={`font-medium ${
                    isAnalyzed ? 'text-gray-800' : 'text-gray-600'
                  }`}>
                    {emotion.name}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`text-sm font-semibold ${
                    isAnalyzed ? 'text-blue-600' : 'text-gray-500'
                  }`}>
                    {(emotion.score * 100).toFixed(1)}%
                  </span>
                  {isAnalyzed && (
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" title="AI Analyzed"></div>
                  )}
                </div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-500 ${
                    isAnalyzed
                      ? 'bg-gradient-to-r from-blue-400 to-blue-600'
                      : 'bg-gradient-to-r from-gray-300 to-gray-400'
                  }`}
                  style={{ width: `${emotion.score * 100}%` }}
                ></div>
              </div>
            </div>
          );
        })}
      </div>
      
      {displayEmotions.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <p>No emotion data available.</p>
        </div>
      )}
      
      {analyzedEmotions.length > 0 && (
        <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center space-x-2 text-sm text-blue-700">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span className="font-medium">
              {analyzedEmotions.length} emotion{analyzedEmotions.length !== 1 ? 's' : ''} analyzed by AI
            </span>
          </div>
        </div>
      )}
    </div>
  );
}
